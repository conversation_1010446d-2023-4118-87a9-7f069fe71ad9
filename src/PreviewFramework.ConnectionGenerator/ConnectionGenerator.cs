using System;
using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using PreviewFramework.Tooling;

namespace PreviewFramework.ConnectionGenerator
{
    [Generator]
    public class ConnectionGenerator : ISourceGenerator
    {
        public void Initialize(GeneratorInitializationContext context)
        {
            // No initialization required
        }

        public void Execute(GeneratorExecutionContext context)
        {
            try
            {
                // Get the connection string from the settings file
                string? connectionString = ConnectionSettingsJson.GetAppConnectionString();

                // Generate the PreviewToolingConnection class
                string sourceCode = GenerateConnectionClass(connectionString);

                // Add the generated source to the compilation
                context.AddSource("PreviewToolingConnection.g.cs", SourceText.From(sourceCode, Encoding.UTF8));
            }
            catch
            {
                // If any error occurs, generate the class with null connection string
                string sourceCode = GenerateConnectionClass(null);
                context.AddSource("PreviewToolingConnection.g.cs", SourceText.From(sourceCode, Encoding.UTF8));
            }
        }

        private static string GenerateConnectionClass(string? connectionString)
        {
            string connectionStringValue = connectionString != null 
                ? $"\"{EscapeString(connectionString)}\""
                : "null";

            return $@"// <auto-generated />
#nullable enable

namespace PreviewFramework
{{
    /// <summary>
    /// Generated class containing the preview tooling connection string.
    /// </summary>
    public static class PreviewToolingConnection
    {{
        /// <summary>
        /// The connection string for the preview tooling, or null if not available.
        /// </summary>
        public static readonly string? ConnectionString = {connectionStringValue};
    }}
}}
";
        }

        private static string EscapeString(string input)
        {
            return input.Replace("\\", "\\\\").Replace("\"", "\\\"");
        }
    }
}
