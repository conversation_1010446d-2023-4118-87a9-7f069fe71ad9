<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <IncludeBuildOutput>false</IncludeBuildOutput>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>PreviewFramework.ConnectionGenerator</PackageId>
    <PackageDescription>Source generator for PreviewFramework connection settings</PackageDescription>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" PrivateAssets="all" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.5.0" PrivateAssets="all" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PreviewFramework.Tooling\PreviewFramework.Tooling.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Analyzer Include="$(OutputPath)\$(AssemblyName).dll" />
  </ItemGroup>

</Project>
